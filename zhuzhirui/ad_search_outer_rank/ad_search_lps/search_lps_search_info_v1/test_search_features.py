#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试搜索特征提取功能
"""

import tensorflow as tf
import numpy as np
import sys
import os

# 添加当前目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_search_feature_extraction():
    """测试搜索特征提取功能"""
    
    # 模拟配置
    class MockConfig:
        def __init__(self):
            self.klearn_conf = type('', (), {})()
            self.klearn_conf.embedding_size = 16
            self.klearn_conf.fields = 259  # 总特征数
            self.klearn_conf.real_dense_fields = [64, 2, 16, 2, 32, 32, 128, 1, 1, 64, 32, 12, 32, 16, 15]
    
    # 模拟模型类
    class MockModel:
        def __init__(self):
            self._config = MockConfig()
        
        def extract_search_features(self, sparse_input):
            """从 sparse embedding 中提取搜索相关特征"""
            embedding_size = self._config.klearn_conf.embedding_size  # 16
            
            # 搜索特征的 field 位置
            search_fields = [
                # 用户搜索特征
                94, 96, 109, 115, 116, 117, 118, 119, 127,
                # 照片搜索特征
                199, 200, 201, 202, 203, 204, 205,
                # 组合搜索特征
                213, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 251, 254, 255, 256
            ]
            
            print("Extracting {} search features from sparse embedding".format(len(search_fields)))
            
            # 提取每个搜索特征的 embedding
            search_feature_slices = []
            for field_id in search_fields:
                start_idx = field_id * embedding_size
                end_idx = (field_id + 1) * embedding_size
                feature_slice = tf.slice(sparse_input, [0, start_idx], [-1, embedding_size])
                search_feature_slices.append(feature_slice)
            
            # 拼接所有搜索特征
            search_features = tf.concat(search_feature_slices, axis=1)
            print("search_features shape: {}".format(search_features.get_shape()))
            
            return search_features
    
    # 创建测试数据
    batch_size = 32
    total_sparse_dim = 259 * 16  # 总的 sparse 特征维度
    
    # 模拟 sparse_input
    sparse_input = tf.placeholder(tf.float32, [batch_size, total_sparse_dim], name="sparse_input")
    
    # 创建模型实例
    model = MockModel()
    
    # 提取搜索特征
    search_features = model.extract_search_features(sparse_input)
    
    # 模拟 share_bottom 网络结构
    dnn_net_size = [1024, 256, 128, 2]  # 模拟网络结构
    share_num = 1  # 模拟 share_bottom 层数

    def fc_layer(input_tensor, input_size, layer_size, name):
        with tf.variable_scope(name):
            w = tf.get_variable('w', [input_size, layer_size],
                              initializer=tf.random_normal_initializer(stddev=1.0/tf.sqrt(float(input_size))))
            b = tf.get_variable('b', [layer_size], initializer=tf.zeros_initializer())
            output = tf.add(tf.matmul(input_tensor, w), b)
            return tf.nn.relu(output) if layer_size != 2 else output  # 最后一层不加激活

    # 构建搜索特征的 share_bottom 网络
    search_input = search_features
    search_input_size = search_features.get_shape().as_list()[1]

    # share_bottom 层
    for i in range(share_num):
        layer_size = dnn_net_size[i]
        search_input = fc_layer(search_input, search_input_size, layer_size, f"search_share_bottom_layer_{i}")
        search_input_size = layer_size

    # 搜索特征的 ctcvr head
    search_ctcvr_input = search_input
    search_ctcvr_input_size = search_input_size

    for i in range(share_num, len(dnn_net_size)):
        layer_size = dnn_net_size[i]
        search_ctcvr_input = fc_layer(search_ctcvr_input, search_ctcvr_input_size, layer_size, f"search_ctcvr_layer_{i}")
        search_ctcvr_input_size = layer_size

    search_ctcvr_logits = search_ctcvr_input

    # 模拟原始 ctcvr logits
    original_ctcvr_logits = tf.placeholder(tf.float32, [batch_size, 2], name="original_ctcvr_logits")

    # 增益建模：搜索 logits + 原始 logits
    enhanced_ctcvr_logits = tf.add(original_ctcvr_logits, search_ctcvr_logits, name="enhanced_ctcvr_logits")

    print("search_ctcvr_logits shape: {}".format(search_ctcvr_logits.get_shape()))
    print("enhanced_ctcvr_logits shape: {}".format(enhanced_ctcvr_logits.get_shape()))
    
    # 创建会话并测试
    with tf.Session() as sess:
        sess.run(tf.global_variables_initializer())
        
        # 生成随机测试数据
        test_data = np.random.randn(batch_size, total_sparse_dim).astype(np.float32)
        
        # 生成模拟的原始 ctcvr logits
        original_logits_data = np.random.randn(batch_size, 2).astype(np.float32)

        # 运行测试
        search_feat_result, search_logits_result, enhanced_logits_result = sess.run(
            [search_features, search_ctcvr_logits, enhanced_ctcvr_logits],
            feed_dict={sparse_input: test_data, original_ctcvr_logits: original_logits_data}
        )

        print("\n=== 测试结果 ===")
        print("输入 sparse_input shape:", test_data.shape)
        print("提取的 search_features shape:", search_feat_result.shape)
        print("搜索 ctcvr logits shape:", search_logits_result.shape)
        print("增强后的 ctcvr logits shape:", enhanced_logits_result.shape)
        print("搜索特征数量:", search_feat_result.shape[1] // 16)

        # 验证特征提取的正确性
        expected_search_dim = 37 * 16  # 37个搜索特征，每个16维
        assert search_feat_result.shape[1] == expected_search_dim, f"搜索特征维度错误: {search_feat_result.shape[1]} != {expected_search_dim}"

        # 验证 logits 输出
        assert search_logits_result.shape[1] == 2, f"搜索 logits 维度错误: {search_logits_result.shape[1]} != 2"
        assert enhanced_logits_result.shape[1] == 2, f"增强 logits 维度错误: {enhanced_logits_result.shape[1]} != 2"

        # 验证增益效果
        logits_diff = enhanced_logits_result - original_logits_data
        assert np.allclose(logits_diff, search_logits_result, rtol=1e-5), "增益计算错误"

        print("✅ 所有测试通过！")

        # 显示搜索特征的统计信息
        print("\n=== 搜索特征统计 ===")
        print("搜索特征均值:", np.mean(search_feat_result))
        print("搜索特征标准差:", np.std(search_feat_result))
        print("搜索 logits 均值:", np.mean(search_logits_result))
        print("搜索 logits 标准差:", np.std(search_logits_result))
        print("增益幅度 (搜索logits的绝对值均值):", np.mean(np.abs(search_logits_result)))

def print_search_feature_mapping():
    """打印搜索特征的映射关系"""
    search_features = [
        (94, "ExtractSearchQuerySource", "用户"),
        (96, "ExtractSearchReferPhotoId", "用户"),
        (109, "ExtractSearchQueryCategoryCalss3", "用户"),
        (115, "ExtractSearchQueryCategoryCalss2", "用户"),
        (116, "ExtractQuery", "用户"),
        (117, "ExtractQuerytoken", "用户"),
        (118, "ExtractSearchFromPage", "用户"),
        (119, "ExtractSearchPosId", "用户"),
        (127, "ExtractSearchEnterSource", "用户"),
        (199, "ExtractSearchRecallMatchtype", "照片"),
        (200, "ExtractSearchRecallRelevance", "照片"),
        (201, "ExtractSearchRecallStrategy", "照片"),
        (202, "ExtractSearchRecallStrategyType", "照片"),
        (203, "ExtractSearchRewriteQuery", "照片"),
        (204, "ExtractSearchQrScore", "照片"),
        (205, "ExtractSearchExtendType", "照片"),
        (213, "ExtractSearchKboxType", "组合"),
        (235, "ExtractSearchPhotoPname", "组合"),
        (236, "ExtractSearchPhotoPname2", "组合"),
        (237, "ExtractSearchPhotoAsr", "组合"),
        (238, "ExtractSearchPhotoAsr2", "组合"),
        (239, "ExtractSearchPhotoCname", "组合"),
        (240, "ExtractSearchPhotoCname2", "组合"),
        (241, "ExtractSearchPhotoDescription", "组合"),
        (242, "ExtractSearchPhotoDescription2", "组合"),
        (243, "ExtractSearchPhotoOcr", "组合"),
        (244, "ExtractSearchPhotoOcr2", "组合"),
        (245, "ExtractSearchPhotoOcrTitle", "组合"),
        (246, "ExtractSearchPhotoOcrTitle2", "组合"),
        (247, "ExtractSearchPhotoSlogan", "组合"),
        (248, "ExtractSearchPhotoSlogan2", "组合"),
        (251, "ExtractSearchBidword", "组合"),
        (254, "ExtractSearchParserTextTokenV1", "组合"),
        (255, "ExtractSearchParserTextV1", "组合"),
        (256, "ExtractSearchQueryCombineMatchNum", "组合")
    ]
    
    print("\n=== 搜索特征映射表 ===")
    print("Field ID | 特征名称 | 类别 | Embedding 位置")
    print("-" * 80)
    
    for field_id, feature_name, category in search_features:
        start_pos = field_id * 16
        end_pos = (field_id + 1) * 16 - 1
        print(f"{field_id:8d} | {feature_name:35s} | {category:4s} | [{start_pos:4d}:{end_pos:4d}]")
    
    print(f"\n总计: {len(search_features)} 个搜索特征")
    print(f"总维度: {len(search_features)} × 16 = {len(search_features) * 16}")

if __name__ == "__main__":
    print("🐾 开始测试搜索特征提取功能...")
    
    # 打印特征映射
    print_search_feature_mapping()
    
    # 运行测试
    test_search_feature_extraction()
    
    print("\n🎉 测试完成！")
